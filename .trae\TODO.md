# TODO:

- [ ] user-deletion-script: Create a comprehensive user deletion script that handles Supabase database cleanup (**IN PROGRESS**) (priority: High)
- [ ] identify-tables: Identify all user-related tables and their foreign key dependencies (priority: High)
- [ ] delete-supabase-data: Delete user data from Supabase tables in correct order (handle foreign keys) (priority: High)
- [ ] test-and-confirm: Test the script and provide confirmation of successful deletion (priority: High)
- [ ] clear-local-cache: Clear local caches, session storage, and temporary files (priority: Medium)
- [ ] reset-counters: Reset user counters and sequences if needed (priority: Medium)
