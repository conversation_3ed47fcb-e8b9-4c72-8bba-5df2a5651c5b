# Jobbify Environment Variables Template
# Copy this file to .env and fill in your actual values
# DO NOT commit .env to version control

# Supabase Configuration
EXPO_PUBLIC_SUPABASE_URL=your_supabase_project_url_here
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Job API Keys
EXPO_PUBLIC_RAPIDAPI_KEY=your_rapidapi_key_here
EXPO_PUBLIC_REMOTEOK_API_KEY=your_remoteok_key_here
EXPO_PUBLIC_ASHBY_API_KEY=your_ashby_key_here

# AI Service Keys
EXPO_PUBLIC_OPENROUTER_API_KEY=your_openrouter_key_here
EXPO_PUBLIC_OPENAI_API_KEY=your_openai_key_here

# Google OAuth Configuration
EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID=your_google_web_client_id
EXPO_PUBLIC_GOOGLE_IOS_CLIENT_ID=your_google_ios_client_id

# File Processing
EXPO_PUBLIC_FILE_EXTRACTION_API_KEY=your_file_extraction_key
EXPO_PUBLIC_PDFCO_API_KEY=your_pdfco_key

# Backend URLs
EXPO_PUBLIC_API_URL=http://localhost:8000
EXPO_PUBLIC_FILE_TEXT_BACKEND_URL=http://localhost:5000

# Site Configuration
EXPO_PUBLIC_SITE_URL=http://localhost
EXPO_PUBLIC_SITE_NAME=Jobbify
