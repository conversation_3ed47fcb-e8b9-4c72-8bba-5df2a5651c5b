/**
 * Test script for the new job filter system
 * This script validates that the filter system works correctly
 */

const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = 'https://ubueawlkwlvgzxcslats.supabase.co';
const supabaseKey = process.env.SUPABASE_ANON_KEY || 'your-anon-key-here';
const supabase = createClient(supabaseUrl, supabaseKey);

async function testFilterSystem() {
  console.log('🧪 Testing Job Filter System...\n');

  try {
    // Test 1: Check if jobs exist
    console.log('1️⃣ Testing job data availability...');
    const { data: jobs, error: jobsError } = await supabase
      .from('jobs')
      .select('*')
      .limit(5);

    if (jobsError) {
      console.error('❌ Error fetching jobs:', jobsError);
      return;
    }

    console.log(`✅ Found ${jobs.length} jobs in database`);
    jobs.forEach(job => {
      console.log(`   - ${job.title} at ${job.company} (${job.job_type}, ${job.experience_level})`);
    });

    // Test 2: Test the filtered jobs function without user preferences
    console.log('\n2️⃣ Testing filtered jobs function (no user preferences)...');
    const { data: defaultJobs, error: defaultError } = await supabase
      .rpc('get_filtered_jobs', {
        p_user_id: '00000000-0000-0000-0000-000000000000',
        p_limit: 5,
        p_offset: 0
      });

    if (defaultError) {
      console.error('❌ Error calling get_filtered_jobs:', defaultError);
      return;
    }

    console.log(`✅ get_filtered_jobs returned ${defaultJobs.length} jobs`);
    defaultJobs.forEach(job => {
      console.log(`   - ${job.title} at ${job.company} (Score: ${job.relevance_score})`);
    });

    // Test 3: Test job caching
    console.log('\n3️⃣ Testing job caching system...');
    
    // Check if cache table exists and is accessible
    const { data: cacheData, error: cacheError } = await supabase
      .from('filtered_job_cache')
      .select('*')
      .limit(1);

    if (cacheError) {
      console.error('❌ Error accessing cache table:', cacheError);
    } else {
      console.log(`✅ Cache table accessible (${cacheData.length} entries)`);
    }

    // Test 4: Test user job interactions table
    console.log('\n4️⃣ Testing user job interactions table...');
    
    const { data: interactionsData, error: interactionsError } = await supabase
      .from('user_job_interactions')
      .select('*')
      .limit(1);

    if (interactionsError) {
      console.error('❌ Error accessing interactions table:', interactionsError);
    } else {
      console.log(`✅ Interactions table accessible (${interactionsData.length} entries)`);
    }

    // Test 5: Test user preferences table
    console.log('\n5️⃣ Testing user job preferences table...');
    
    const { data: preferencesData, error: preferencesError } = await supabase
      .from('user_job_preferences')
      .select('*')
      .limit(1);

    if (preferencesError) {
      console.error('❌ Error accessing preferences table:', preferencesError);
    } else {
      console.log(`✅ Preferences table accessible (${preferencesData.length} entries)`);
    }

    // Test 6: Validate job data structure
    console.log('\n6️⃣ Validating job data structure...');
    
    if (jobs.length > 0) {
      const sampleJob = jobs[0];
      const requiredFields = [
        'id', 'title', 'company', 'job_type', 'experience_level', 
        'industry', 'company_size', 'salary_min', 'salary_max', 'remote'
      ];
      
      const missingFields = requiredFields.filter(field => !(field in sampleJob));
      
      if (missingFields.length === 0) {
        console.log('✅ Job data structure is valid');
      } else {
        console.log(`❌ Missing fields in job data: ${missingFields.join(', ')}`);
      }
    }

    // Test 7: Test relevance scoring
    console.log('\n7️⃣ Testing relevance scoring...');
    
    const { data: scoredJobs, error: scoringError } = await supabase
      .rpc('get_filtered_jobs', {
        p_user_id: '00000000-0000-0000-0000-000000000000',
        p_limit: 3,
        p_offset: 0
      });

    if (scoringError) {
      console.error('❌ Error testing relevance scoring:', scoringError);
    } else {
      console.log('✅ Relevance scoring working:');
      scoredJobs.forEach(job => {
        console.log(`   - ${job.title}: ${job.relevance_score} points`);
      });
    }

    console.log('\n🎉 Filter system test completed successfully!');
    console.log('\n📋 Summary:');
    console.log('   ✅ Database tables created and accessible');
    console.log('   ✅ Sample job data inserted');
    console.log('   ✅ Filtering function working');
    console.log('   ✅ Relevance scoring operational');
    console.log('   ✅ Caching system ready');
    console.log('   ✅ User interaction tracking ready');

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Run the test
if (require.main === module) {
  testFilterSystem();
}

module.exports = { testFilterSystem };
